<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cards', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Card name (e.g., "Thẻ Viettel 100k")
            $table->string('type'); // 'mobile', 'game'
            $table->string('operator_code'); // VTT, VNP, VMS for mobile; FF, PUBG for game
            $table->decimal('face_value', 15, 2); // Original card value
            $table->decimal('sell_price', 15, 2); // Price to sell to customers
            $table->decimal('discount_percentage', 5, 2)->default(0); // Discount percentage
            $table->string('image_url')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('stock_quantity')->default(0); // Available stock
            $table->integer('sold_quantity')->default(0); // Total sold
            $table->json('metadata')->nullable(); // Additional card information
            $table->timestamps();

            $table->index(['type', 'operator_code', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cards');
    }
};
