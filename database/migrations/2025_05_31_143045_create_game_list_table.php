<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_list', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Free Fire, PUBG Mobile, Liên Quân Mobile, etc.
            $table->string('code')->unique(); // FF, PUBG, LQM, etc.
            $table->string('logo_url')->nullable();
            $table->text('description')->nullable();
            $table->json('supported_amounts'); // [20000, 50000, 100000, 200000, 500000]
            $table->string('id_format_description'); // "Game ID (ví dụ: 123456789)"
            $table->string('id_validation_regex')->nullable(); // Regex to validate game ID format
            $table->decimal('fee_percentage', 5, 2)->default(0); // Fee percentage for top-up
            $table->boolean('is_active')->default(true);
            $table->json('api_config')->nullable(); // API configuration for this game
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_list');
    }
};
