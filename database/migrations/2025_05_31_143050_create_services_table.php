<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Service name
            $table->string('type'); // 'telecom', 'game', 'card'
            $table->string('provider'); // External API provider name
            $table->json('config'); // API configuration (endpoints, keys, etc.)
            $table->boolean('is_active')->default(true);
            $table->decimal('success_rate', 5, 2)->default(100.00); // Success rate percentage
            $table->integer('priority')->default(1); // Higher priority = preferred service
            $table->json('supported_operators')->nullable(); // For telecom services
            $table->json('supported_games')->nullable(); // For game services
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
