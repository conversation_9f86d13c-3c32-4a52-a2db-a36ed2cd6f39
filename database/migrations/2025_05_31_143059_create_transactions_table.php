<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique(); // Unique transaction identifier
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['buy_card', 'exchange_card', 'mobile_topup', 'game_topup', 'wallet_deposit', 'wallet_withdrawal']);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->decimal('amount', 15, 2); // Transaction amount
            $table->decimal('fee', 15, 2)->default(0); // Transaction fee
            $table->decimal('final_amount', 15, 2); // Amount after fee
            $table->string('payment_method')->nullable(); // 'wallet', 'bank_transfer', etc.
            $table->json('details'); // Transaction-specific details (card info, phone number, game ID, etc.)
            $table->string('external_transaction_id')->nullable(); // External API transaction ID
            $table->text('notes')->nullable(); // Admin notes or error messages
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'type', 'status']);
            $table->index(['transaction_id']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
