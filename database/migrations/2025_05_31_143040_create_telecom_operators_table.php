<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telecom_operators', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Viettel, Vinaphone, Mobifone, etc.
            $table->string('code')->unique(); // VTT, VNP, VMS, etc.
            $table->string('logo_url')->nullable();
            $table->json('supported_amounts'); // [10000, 20000, 50000, 100000, 200000, 500000]
            $table->decimal('fee_percentage', 5, 2)->default(0); // Fee percentage for top-up
            $table->boolean('is_active')->default(true);
            $table->json('api_config')->nullable(); // API configuration for this operator
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telecom_operators');
    }
};
