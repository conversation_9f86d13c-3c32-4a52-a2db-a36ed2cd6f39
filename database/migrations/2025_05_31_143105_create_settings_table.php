<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Setting key
            $table->text('value')->nullable(); // Setting value (can be JSON)
            $table->string('type')->default('string'); // 'string', 'number', 'boolean', 'json'
            $table->string('group')->default('general'); // Setting group for organization
            $table->text('description')->nullable(); // Human-readable description
            $table->boolean('is_public')->default(false); // Whether setting can be accessed by frontend
            $table->timestamps();

            $table->index(['group', 'key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
