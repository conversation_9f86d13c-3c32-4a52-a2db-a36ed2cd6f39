<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('wallet_balance', 15, 2)->default(0)->after('email_verified_at');
            $table->enum('role', ['user', 'admin'])->default('user')->after('wallet_balance');
            $table->string('phone')->nullable()->after('role');
            $table->timestamp('last_login_at')->nullable()->after('phone');
            $table->boolean('is_active')->default(true)->after('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['wallet_balance', 'role', 'phone', 'last_login_at', 'is_active']);
        });
    }
};
