<?php

namespace Database\Seeders;

use App\Models\Card;
use Illuminate\Database\Seeder;

class CardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cards = [];

        // Mobile cards for each operator
        $operators = ['VTT', 'VNP', 'VMS', 'VNM', 'GMB'];
        $operatorNames = [
            'VTT' => 'Viettel',
            'VNP' => 'Vinaphone',
            'VMS' => 'Mobifone',
            'VNM' => 'Vietnamobile',
            'GMB' => 'Gmobile'
        ];
        $amounts = [10000, 20000, 50000, 100000, 200000, 500000];

        foreach ($operators as $operator) {
            foreach ($amounts as $amount) {
                $cards[] = [
                    'name' => "Thẻ {$operatorNames[$operator]} " . number_format($amount) . 'đ',
                    'type' => 'mobile',
                    'operator_code' => $operator,
                    'face_value' => $amount,
                    'sell_price' => $amount * 0.98, // 2% discount
                    'discount_percentage' => 2.0,
                    'image_url' => "/images/cards/{$operator}_{$amount}.png",
                    'is_active' => true,
                    'stock_quantity' => 100,
                    'sold_quantity' => 0,
                    'metadata' => [
                        'category' => 'mobile_card',
                        'operator' => $operatorNames[$operator],
                    ],
                ];
            }
        }

        // Game cards
        $games = ['FF', 'PUBG', 'LQM', 'ML', 'GI'];
        $gameNames = [
            'FF' => 'Free Fire',
            'PUBG' => 'PUBG Mobile',
            'LQM' => 'Liên Quân Mobile',
            'ML' => 'Mobile Legends',
            'GI' => 'Genshin Impact'
        ];
        $gameAmounts = [20000, 50000, 100000, 200000, 500000];

        foreach ($games as $game) {
            foreach ($gameAmounts as $amount) {
                $cards[] = [
                    'name' => "Thẻ {$gameNames[$game]} " . number_format($amount) . 'đ',
                    'type' => 'game',
                    'operator_code' => $game,
                    'face_value' => $amount,
                    'sell_price' => $amount * 0.97, // 3% discount
                    'discount_percentage' => 3.0,
                    'image_url' => "/images/cards/{$game}_{$amount}.png",
                    'is_active' => true,
                    'stock_quantity' => 50,
                    'sold_quantity' => 0,
                    'metadata' => [
                        'category' => 'game_card',
                        'game' => $gameNames[$game],
                    ],
                ];
            }
        }

        foreach ($cards as $card) {
            Card::create($card);
        }
    }
}
