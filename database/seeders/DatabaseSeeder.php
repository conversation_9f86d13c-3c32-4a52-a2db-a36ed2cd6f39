<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'wallet_balance' => 1000000,
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
            'wallet_balance' => 100000,
        ]);

        // Seed application data
        $this->call([
            SettingSeeder::class,
            TelecomOperatorSeeder::class,
            GameSeeder::class,
            CardSeeder::class,
        ]);
    }
}
