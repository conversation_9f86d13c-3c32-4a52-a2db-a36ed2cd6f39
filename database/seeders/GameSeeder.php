<?php

namespace Database\Seeders;

use App\Models\Game;
use Illuminate\Database\Seeder;

class GameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $games = [
            [
                'name' => 'Free Fire',
                'code' => 'FF',
                'logo_url' => '/images/games/freefire.png',
                'description' => 'Nạp kim cương Free Fire nhanh chóng, an toàn',
                'supported_amounts' => [20000, 50000, 100000, 200000, 500000, 1000000],
                'id_format_description' => 'Player ID (ví dụ: 123456789)',
                'id_validation_regex' => '/^\d{9,12}$/',
                'fee_percentage' => 1.5,
                'is_active' => true,
                'sort_order' => 1,
                'api_config' => [
                    'endpoint' => 'https://api.freefire.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'PUBG Mobile',
                'code' => 'PUBG',
                'logo_url' => '/images/games/pubg.png',
                'description' => 'Nạp UC PUBG Mobile giá rẻ, uy tín',
                'supported_amounts' => [20000, 50000, 100000, 200000, 500000, 1000000],
                'id_format_description' => 'Character ID (ví dụ: 5123456789)',
                'id_validation_regex' => '/^\d{10,12}$/',
                'fee_percentage' => 1.5,
                'is_active' => true,
                'sort_order' => 2,
                'api_config' => [
                    'endpoint' => 'https://api.pubgmobile.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Liên Quân Mobile',
                'code' => 'LQM',
                'logo_url' => '/images/games/lienquan.png',
                'description' => 'Nạp quân huy Liên Quân Mobile nhanh nhất',
                'supported_amounts' => [20000, 50000, 100000, 200000, 500000, 1000000],
                'id_format_description' => 'Game ID (ví dụ: 123456789)',
                'id_validation_regex' => '/^\d{8,10}$/',
                'fee_percentage' => 1.0,
                'is_active' => true,
                'sort_order' => 3,
                'api_config' => [
                    'endpoint' => 'https://api.lienquan.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Mobile Legends',
                'code' => 'ML',
                'logo_url' => '/images/games/mobilelegends.png',
                'description' => 'Nạp kim cương Mobile Legends giá tốt',
                'supported_amounts' => [20000, 50000, 100000, 200000, 500000, 1000000],
                'id_format_description' => 'User ID (ví dụ: 123456789) và Zone ID (ví dụ: 1234)',
                'id_validation_regex' => '/^\d{8,12}$/',
                'fee_percentage' => 1.5,
                'is_active' => true,
                'sort_order' => 4,
                'api_config' => [
                    'endpoint' => 'https://api.mobilelegends.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Genshin Impact',
                'code' => 'GI',
                'logo_url' => '/images/games/genshin.png',
                'description' => 'Nạp Genesis Crystal Genshin Impact',
                'supported_amounts' => [50000, 100000, 200000, 500000, 1000000, 2000000],
                'id_format_description' => 'UID (ví dụ: 123456789)',
                'id_validation_regex' => '/^\d{9}$/',
                'fee_percentage' => 2.0,
                'is_active' => true,
                'sort_order' => 5,
                'api_config' => [
                    'endpoint' => 'https://api.genshin.com/topup',
                    'timeout' => 30,
                ],
            ],
        ];

        foreach ($games as $game) {
            Game::create($game);
        }
    }
}
