<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General settings
            [
                'key' => 'app_name',
                'value' => 'NapGame Pro',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Application name',
                'is_public' => true,
            ],
            [
                'key' => 'app_description',
                'value' => 'Nạp thẻ game và điện thoại n<PERSON>h chóng, uy tín',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Application description',
                'is_public' => true,
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Contact email',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '0123456789',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Contact phone number',
                'is_public' => true,
            ],

            // Transaction settings
            [
                'key' => 'min_topup_amount',
                'value' => '10000',
                'type' => 'number',
                'group' => 'transaction',
                'description' => 'Minimum top-up amount',
                'is_public' => true,
            ],
            [
                'key' => 'max_topup_amount',
                'value' => '5000000',
                'type' => 'number',
                'group' => 'transaction',
                'description' => 'Maximum top-up amount',
                'is_public' => true,
            ],
            [
                'key' => 'default_transaction_fee',
                'value' => '2.0',
                'type' => 'number',
                'group' => 'transaction',
                'description' => 'Default transaction fee percentage',
                'is_public' => false,
            ],
            [
                'key' => 'card_exchange_rate',
                'value' => '85.0',
                'type' => 'number',
                'group' => 'transaction',
                'description' => 'Card to cash exchange rate percentage',
                'is_public' => true,
            ],

            // API settings
            [
                'key' => 'api_timeout',
                'value' => '30',
                'type' => 'number',
                'group' => 'api',
                'description' => 'API request timeout in seconds',
                'is_public' => false,
            ],
            [
                'key' => 'api_retry_attempts',
                'value' => '3',
                'type' => 'number',
                'group' => 'api',
                'description' => 'Number of API retry attempts',
                'is_public' => false,
            ],

            // Maintenance settings
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'maintenance',
                'description' => 'Enable maintenance mode',
                'is_public' => true,
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'Hệ thống đang bảo trì, vui lòng quay lại sau.',
                'type' => 'string',
                'group' => 'maintenance',
                'description' => 'Maintenance mode message',
                'is_public' => true,
            ],

            // Feature flags
            [
                'key' => 'enable_card_exchange',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'features',
                'description' => 'Enable card to cash exchange feature',
                'is_public' => true,
            ],
            [
                'key' => 'enable_mobile_topup',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'features',
                'description' => 'Enable mobile top-up feature',
                'is_public' => true,
            ],
            [
                'key' => 'enable_game_topup',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'features',
                'description' => 'Enable game top-up feature',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
