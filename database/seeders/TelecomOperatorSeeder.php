<?php

namespace Database\Seeders;

use App\Models\TelecomOperator;
use Illuminate\Database\Seeder;

class TelecomOperatorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $operators = [
            [
                'name' => 'Viettel',
                'code' => 'VTT',
                'logo_url' => '/images/operators/viettel.png',
                'supported_amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000],
                'fee_percentage' => 2.0,
                'is_active' => true,
                'sort_order' => 1,
                'api_config' => [
                    'endpoint' => 'https://api.viettel.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Vinaphone',
                'code' => 'VNP',
                'logo_url' => '/images/operators/vinaphone.png',
                'supported_amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000],
                'fee_percentage' => 2.0,
                'is_active' => true,
                'sort_order' => 2,
                'api_config' => [
                    'endpoint' => 'https://api.vinaphone.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Mobifone',
                'code' => 'VMS',
                'logo_url' => '/images/operators/mobifone.png',
                'supported_amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000],
                'fee_percentage' => 2.0,
                'is_active' => true,
                'sort_order' => 3,
                'api_config' => [
                    'endpoint' => 'https://api.mobifone.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Vietnamobile',
                'code' => 'VNM',
                'logo_url' => '/images/operators/vietnamobile.png',
                'supported_amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000],
                'fee_percentage' => 2.5,
                'is_active' => true,
                'sort_order' => 4,
                'api_config' => [
                    'endpoint' => 'https://api.vietnamobile.com/topup',
                    'timeout' => 30,
                ],
            ],
            [
                'name' => 'Gmobile',
                'code' => 'GMB',
                'logo_url' => '/images/operators/gmobile.png',
                'supported_amounts' => [10000, 20000, 50000, 100000, 200000, 500000],
                'fee_percentage' => 3.0,
                'is_active' => true,
                'sort_order' => 5,
                'api_config' => [
                    'endpoint' => 'https://api.gmobile.com/topup',
                    'timeout' => 30,
                ],
            ],
        ];

        foreach ($operators as $operator) {
            TelecomOperator::create($operator);
        }
    }
}
