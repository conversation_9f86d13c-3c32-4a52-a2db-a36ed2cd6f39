<?php

use App\Http\Controllers\CardController;
use App\Http\Controllers\ExchangeController;
use App\Http\Controllers\TopupController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\Admin\AdminController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');

    // Card routes
    Route::get('/cards', [CardController::class, 'index'])->name('cards.index');
    Route::post('/cards/purchase', [CardController::class, 'purchase'])->name('cards.purchase');
    Route::get('/cards/{card}', [CardController::class, 'show'])->name('cards.show');

    // Transaction routes
    Route::get('/transactions', [TransactionController::class, 'index'])->name('transactions.index');
    Route::get('/transactions/{transaction}', [TransactionController::class, 'show'])->name('transactions.show');
    Route::get('/api/transactions/stats', [TransactionController::class, 'stats'])->name('transactions.stats');

    // Top-up routes
    Route::get('/topup/mobile', [TopupController::class, 'mobile'])->name('topup.mobile');
    Route::post('/topup/mobile', [TopupController::class, 'processMobile'])->name('topup.mobile.process');
    Route::get('/topup/game', [TopupController::class, 'game'])->name('topup.game');
    Route::post('/topup/game', [TopupController::class, 'processGame'])->name('topup.game.process');

    // Exchange routes
    Route::get('/exchange', [ExchangeController::class, 'index'])->name('exchange.index');
    Route::post('/exchange', [ExchangeController::class, 'process'])->name('exchange.process');
    Route::get('/api/exchange/rate', [ExchangeController::class, 'getExchangeRate'])->name('exchange.rate');

    // Admin routes
    Route::middleware(['admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/transactions', [AdminController::class, 'transactions'])->name('transactions');
        Route::post('/exchange/{transaction}/approve', [ExchangeController::class, 'approve'])->name('exchange.approve');
        Route::post('/exchange/{transaction}/reject', [ExchangeController::class, 'reject'])->name('exchange.reject');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
