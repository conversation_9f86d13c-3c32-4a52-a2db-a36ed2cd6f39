<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'provider',
        'config',
        'is_active',
        'success_rate',
        'priority',
        'supported_operators',
        'supported_games',
    ];

    protected function casts(): array
    {
        return [
            'config' => 'array',
            'is_active' => 'boolean',
            'success_rate' => 'decimal:2',
            'priority' => 'integer',
            'supported_operators' => 'array',
            'supported_games' => 'array',
        ];
    }

    /**
     * Scope for active services
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for services by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for services ordered by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Check if service supports operator
     */
    public function supportsOperator(string $operatorCode): bool
    {
        return in_array($operatorCode, $this->supported_operators ?? []);
    }

    /**
     * Check if service supports game
     */
    public function supportsGame(string $gameCode): bool
    {
        return in_array($gameCode, $this->supported_games ?? []);
    }
}
