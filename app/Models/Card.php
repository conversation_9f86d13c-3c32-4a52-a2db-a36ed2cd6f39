<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Card extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'operator_code',
        'face_value',
        'sell_price',
        'discount_percentage',
        'image_url',
        'is_active',
        'stock_quantity',
        'sold_quantity',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'face_value' => 'decimal:2',
            'sell_price' => 'decimal:2',
            'discount_percentage' => 'decimal:2',
            'is_active' => 'boolean',
            'stock_quantity' => 'integer',
            'sold_quantity' => 'integer',
            'metadata' => 'array',
        ];
    }

    /**
     * Scope for active cards
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for cards by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for cards by operator
     */
    public function scopeByOperator($query, string $operatorCode)
    {
        return $query->where('operator_code', $operatorCode);
    }

    /**
     * Check if card is in stock
     */
    public function isInStock(): bool
    {
        return $this->stock_quantity > 0;
    }

    /**
     * Calculate final price after discount
     */
    public function getFinalPrice(): float
    {
        if ($this->discount_percentage > 0) {
            return $this->sell_price * (1 - $this->discount_percentage / 100);
        }
        return $this->sell_price;
    }

    /**
     * Decrease stock quantity
     */
    public function decreaseStock(int $quantity = 1): bool
    {
        if ($this->stock_quantity >= $quantity) {
            $this->decrement('stock_quantity', $quantity);
            $this->increment('sold_quantity', $quantity);
            return true;
        }
        return false;
    }
}
