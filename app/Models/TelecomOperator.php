<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TelecomOperator extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'logo_url',
        'supported_amounts',
        'fee_percentage',
        'is_active',
        'api_config',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'supported_amounts' => 'array',
            'fee_percentage' => 'decimal:2',
            'is_active' => 'boolean',
            'api_config' => 'array',
            'sort_order' => 'integer',
        ];
    }

    /**
     * Scope for active operators
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered operators
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if amount is supported
     */
    public function supportsAmount(int $amount): bool
    {
        return in_array($amount, $this->supported_amounts ?? []);
    }

    /**
     * Calculate fee for amount
     */
    public function calculateFee(float $amount): float
    {
        return $amount * ($this->fee_percentage / 100);
    }
}
