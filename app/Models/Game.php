<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    use HasFactory;

    protected $table = 'game_list';

    protected $fillable = [
        'name',
        'code',
        'logo_url',
        'description',
        'supported_amounts',
        'id_format_description',
        'id_validation_regex',
        'fee_percentage',
        'is_active',
        'api_config',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'supported_amounts' => 'array',
            'fee_percentage' => 'decimal:2',
            'is_active' => 'boolean',
            'api_config' => 'array',
            'sort_order' => 'integer',
        ];
    }

    /**
     * Scope for active games
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered games
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if amount is supported
     */
    public function supportsAmount(int $amount): bool
    {
        return in_array($amount, $this->supported_amounts ?? []);
    }

    /**
     * Validate game ID format
     */
    public function validateGameId(string $gameId): bool
    {
        if (empty($this->id_validation_regex)) {
            return true; // No validation required
        }

        return preg_match($this->id_validation_regex, $gameId) === 1;
    }

    /**
     * Calculate fee for amount
     */
    public function calculateFee(float $amount): float
    {
        return $amount * ($this->fee_percentage / 100);
    }
}
