<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TransactionController extends Controller
{
    /**
     * Display user's transaction history
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        $query = Transaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc');

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $transactions = $query->paginate(20)->withQueryString();

        return Inertia::render('Transactions/Index', [
            'transactions' => $transactions,
            'filters' => $request->only(['type', 'status', 'from_date', 'to_date']),
            'transactionTypes' => [
                'buy_card' => 'Mua thẻ',
                'exchange_card' => 'Đổi thẻ',
                'mobile_topup' => 'Nạp điện thoại',
                'game_topup' => 'Nạp game',
                'wallet_deposit' => 'Nạp ví',
                'wallet_withdrawal' => 'Rút ví',
            ],
            'transactionStatuses' => [
                'pending' => 'Đang chờ',
                'processing' => 'Đang xử lý',
                'completed' => 'Hoàn thành',
                'failed' => 'Thất bại',
                'cancelled' => 'Đã hủy',
            ],
        ]);
    }

    /**
     * Show transaction details
     */
    public function show(Transaction $transaction)
    {
        $user = Auth::user();

        // Check if user owns this transaction or is admin
        if ($transaction->user_id !== $user->id && $user->role !== 'admin') {
            abort(403);
        }

        return Inertia::render('Transactions/Show', [
            'transaction' => $transaction->load('user'),
        ]);
    }

    /**
     * Get transaction statistics for dashboard
     */
    public function stats()
    {
        $user = Auth::user();

        $stats = [
            'total_transactions' => Transaction::where('user_id', $user->id)->count(),
            'completed_transactions' => Transaction::where('user_id', $user->id)
                ->where('status', 'completed')->count(),
            'pending_transactions' => Transaction::where('user_id', $user->id)
                ->where('status', 'pending')->count(),
            'total_spent' => Transaction::where('user_id', $user->id)
                ->where('status', 'completed')
                ->sum('final_amount'),
            'this_month_spent' => Transaction::where('user_id', $user->id)
                ->where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('final_amount'),
        ];

        return response()->json($stats);
    }
}
