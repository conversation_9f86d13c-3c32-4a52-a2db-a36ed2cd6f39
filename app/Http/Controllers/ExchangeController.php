<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\TelecomOperator;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ExchangeController extends Controller
{
    /**
     * Show card exchange page
     */
    public function index()
    {
        $operators = TelecomOperator::active()->ordered()->get();
        $exchangeRate = Setting::getValue('card_exchange_rate', 85);

        return Inertia::render('Exchange/Index', [
            'operators' => $operators,
            'exchangeRate' => $exchangeRate,
        ]);
    }

    /**
     * Process card exchange
     */
    public function process(Request $request)
    {
        $request->validate([
            'operator_code' => 'required|string|exists:telecom_operators,code',
            'card_serial' => 'required|string|min:10|max:20',
            'card_code' => 'required|string|min:10|max:20',
            'card_value' => 'required|integer|min:10000',
        ]);

        $user = Auth::user();
        $operator = TelecomOperator::where('code', $request->operator_code)->first();
        $exchangeRate = Setting::getValue('card_exchange_rate', 85);

        if (!$operator || !$operator->is_active) {
            return back()->withErrors(['operator_code' => 'Nhà mạng không được hỗ trợ.']);
        }

        // Calculate exchange amount (e.g., 85% of card value)
        $exchangeAmount = ($request->card_value * $exchangeRate) / 100;

        // Check for duplicate card serial/code
        $existingTransaction = Transaction::where('type', 'exchange_card')
            ->whereJsonContains('details->card_serial', $request->card_serial)
            ->whereJsonContains('details->card_code', $request->card_code)
            ->first();

        if ($existingTransaction) {
            return back()->withErrors(['card_serial' => 'Thẻ này đã được sử dụng trước đó.']);
        }

        DB::transaction(function () use ($user, $operator, $request, $exchangeAmount, $exchangeRate) {
            // Create transaction record (pending status for verification)
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'exchange_card',
                'status' => 'pending',
                'amount' => $request->card_value,
                'fee' => 0,
                'final_amount' => $exchangeAmount,
                'payment_method' => 'card_exchange',
                'details' => [
                    'operator_id' => $operator->id,
                    'operator_name' => $operator->name,
                    'operator_code' => $operator->code,
                    'card_serial' => $request->card_serial,
                    'card_code' => $request->card_code,
                    'card_value' => $request->card_value,
                    'exchange_rate' => $exchangeRate,
                    'exchange_amount' => $exchangeAmount,
                ],
            ]);
        });

        return redirect()->route('transactions.index')
            ->with('success', 'Yêu cầu đổi thẻ đã được gửi! Thẻ sẽ được kiểm tra và tiền sẽ được cộng vào ví sau khi xác nhận.');
    }

    /**
     * Get exchange rate for a card value
     */
    public function getExchangeRate(Request $request)
    {
        $request->validate([
            'card_value' => 'required|integer|min:10000',
        ]);

        $exchangeRate = Setting::getValue('card_exchange_rate', 85);
        $exchangeAmount = ($request->card_value * $exchangeRate) / 100;

        return response()->json([
            'exchange_rate' => $exchangeRate,
            'exchange_amount' => $exchangeAmount,
            'card_value' => $request->card_value,
        ]);
    }

    /**
     * Admin: Approve card exchange
     */
    public function approve(Transaction $transaction)
    {
        $user = Auth::user();

        if ($user->role !== 'admin') {
            abort(403);
        }

        if ($transaction->type !== 'exchange_card' || $transaction->status !== 'pending') {
            return back()->withErrors(['transaction' => 'Giao dịch không hợp lệ.']);
        }

        DB::transaction(function () use ($transaction) {
            // Add money to user wallet
            DB::table('users')
                ->where('id', $transaction->user_id)
                ->increment('wallet_balance', $transaction->final_amount);

            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'processed_at' => now(),
            ]);
        });

        return back()->with('success', 'Đã duyệt giao dịch đổi thẻ thành công.');
    }

    /**
     * Admin: Reject card exchange
     */
    public function reject(Request $request, Transaction $transaction)
    {
        $user = Auth::user();

        if ($user->role !== 'admin') {
            abort(403);
        }

        if ($transaction->type !== 'exchange_card' || $transaction->status !== 'pending') {
            return back()->withErrors(['transaction' => 'Giao dịch không hợp lệ.']);
        }

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $transaction->update([
            'status' => 'failed',
            'notes' => $request->reason,
            'processed_at' => now(),
        ]);

        return back()->with('success', 'Đã từ chối giao dịch đổi thẻ.');
    }
}
