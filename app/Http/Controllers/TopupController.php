<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\TelecomOperator;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class TopupController extends Controller
{
    /**
     * Show mobile top-up page
     */
    public function mobile()
    {
        $operators = TelecomOperator::active()->ordered()->get();

        return Inertia::render('Topup/Mobile', [
            'operators' => $operators,
        ]);
    }

    /**
     * Process mobile top-up
     */
    public function processMobile(Request $request)
    {
        $request->validate([
            'operator_id' => 'required|exists:telecom_operators,id',
            'phone_number' => 'required|regex:/^[0-9]{10,11}$/',
            'amount' => 'required|integer|min:10000',
        ]);

        $user = Auth::user();
        $operator = TelecomOperator::findOrFail($request->operator_id);

        if (!$operator->is_active) {
            return back()->withErrors(['operator' => 'Nhà mạng này hiện không khả dụng.']);
        }

        if (!$operator->supportsAmount($request->amount)) {
            return back()->withErrors(['amount' => 'Mệnh giá không được hỗ trợ.']);
        }

        $fee = $operator->calculateFee($request->amount);
        $totalAmount = $request->amount + $fee;

        if ($user->wallet_balance < $totalAmount) {
            return back()->withErrors(['wallet' => 'Số dư ví không đủ để thực hiện giao dịch.']);
        }

        DB::transaction(function () use ($user, $operator, $request, $totalAmount, $fee) {
            // Deduct from user wallet
            DB::table('users')
                ->where('id', $user->id)
                ->decrement('wallet_balance', $totalAmount);

            // Create transaction record
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'mobile_topup',
                'status' => 'pending',
                'amount' => $request->amount,
                'fee' => $fee,
                'final_amount' => $totalAmount,
                'payment_method' => 'wallet',
                'details' => [
                    'operator_id' => $operator->id,
                    'operator_name' => $operator->name,
                    'operator_code' => $operator->code,
                    'phone_number' => $request->phone_number,
                    'amount' => $request->amount,
                    'fee' => $fee,
                    'total_amount' => $totalAmount,
                ],
            ]);
        });

        return redirect()->route('transactions.index')
            ->with('success', 'Yêu cầu nạp điện thoại đã được gửi! Kiểm tra lịch sử giao dịch để theo dõi.');
    }

    /**
     * Show game top-up page
     */
    public function game()
    {
        $games = Game::active()->ordered()->get();

        return Inertia::render('Topup/Game', [
            'games' => $games,
        ]);
    }

    /**
     * Process game top-up
     */
    public function processGame(Request $request)
    {
        $request->validate([
            'game_id' => 'required|exists:game_list,id',
            'player_id' => 'required|string|max:50',
            'amount' => 'required|integer|min:20000',
            'zone_id' => 'nullable|string|max:20', // For games that require zone ID
        ]);

        /** @var User $user */
        $user = Auth::user();
        $game = Game::findOrFail($request->game_id);

        if (!$game->is_active) {
            return back()->withErrors(['game' => 'Game này hiện không khả dụng.']);
        }

        if (!$game->supportsAmount($request->amount)) {
            return back()->withErrors(['amount' => 'Mệnh giá không được hỗ trợ.']);
        }

        if (!$game->validateGameId($request->player_id)) {
            return back()->withErrors(['player_id' => 'ID game không đúng định dạng.']);
        }

        $fee = $game->calculateFee($request->amount);
        $totalAmount = $request->amount + $fee;

        if ($user->wallet_balance < $totalAmount) {
            return back()->withErrors(['wallet' => 'Số dư ví không đủ để thực hiện giao dịch.']);
        }

        DB::transaction(function () use ($user, $game, $request, $totalAmount, $fee) {
            // Deduct from user wallet
            $user->subtractFromWallet($totalAmount);

            // Create transaction record
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'game_topup',
                'status' => 'pending',
                'amount' => $request->amount,
                'fee' => $fee,
                'final_amount' => $totalAmount,
                'payment_method' => 'wallet',
                'details' => [
                    'game_id' => $game->id,
                    'game_name' => $game->name,
                    'game_code' => $game->code,
                    'player_id' => $request->player_id,
                    'zone_id' => $request->zone_id,
                    'amount' => $request->amount,
                    'fee' => $fee,
                    'total_amount' => $totalAmount,
                ],
            ]);
        });

        return redirect()
            ->route('transactions.index')
            ->with('success', 'Yêu cầu nạp game đã được gửi! Kiểm tra lịch sử giao dịch để theo dõi.');
    }
}
