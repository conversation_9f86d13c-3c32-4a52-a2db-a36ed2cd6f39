<?php

namespace App\Http\Controllers;

use App\Models\Card;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class CardController extends Controller
{
    /**
     * Display card purchase page
     */
    public function index()
    {
        $mobileCards = Card::active()
            ->byType('mobile')
            ->orderBy('operator_code')
            ->orderBy('face_value')
            ->get()
            ->groupBy('operator_code');

        $gameCards = Card::active()
            ->byType('game')
            ->orderBy('operator_code')
            ->orderBy('face_value')
            ->get()
            ->groupBy('operator_code');

        return Inertia::render('Cards/Index', [
            'mobileCards' => $mobileCards,
            'gameCards' => $gameCards,
        ]);
    }

    /**
     * Purchase a card
     */
    public function purchase(Request $request)
    {
        $request->validate([
            'card_id' => 'required|exists:cards,id',
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        /** @var User $user */
        $user = Auth::user();
        $card = Card::findOrFail($request->card_id);

        if (!$card->is_active) {
            return back()->withErrors(['card' => 'Thẻ này hiện không khả dụng.']);
        }

        if (!$card->isInStock()) {
            return back()->withErrors(['card' => 'Thẻ này hiện đã hết hàng.']);
        }

        if ($card->stock_quantity < $request->quantity) {
            return back()->withErrors(['card' => 'Số lượng thẻ không đủ.']);
        }

        $finalPrice = $card->getFinalPrice();
        $totalAmount = $finalPrice * $request->quantity;

        if ($user->wallet_balance < $totalAmount) {
            return back()->withErrors(['wallet' => 'Số dư ví không đủ để thực hiện giao dịch.']);
        }

        DB::transaction(function () use ($user, $card, $request, $totalAmount, $finalPrice) {
            // Deduct from user wallet
            $user->subtractFromWallet($totalAmount);

            // Decrease card stock
            DB::table('cards')
                ->where('id', $card->id)
                ->decrement('stock_quantity', $request->quantity);

            DB::table('cards')
                ->where('id', $card->id)
                ->increment('sold_quantity', $request->quantity);

            // Create transaction record
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'buy_card',
                'status' => 'completed',
                'amount' => $totalAmount,
                'fee' => 0,
                'final_amount' => $totalAmount,
                'payment_method' => 'wallet',
                'details' => [
                    'card_id' => $card->id,
                    'card_name' => $card->name,
                    'card_type' => $card->type,
                    'operator_code' => $card->operator_code,
                    'face_value' => $card->face_value,
                    'unit_price' => $finalPrice,
                    'quantity' => $request->quantity,
                    'total_amount' => $totalAmount,
                ],
                'processed_at' => now(),
            ]);
        });

        return redirect()->route('transactions.index')
            ->with('success', 'Mua thẻ thành công! Kiểm tra lịch sử giao dịch để xem chi tiết.');
    }

    /**
     * Get card details
     */
    public function show(Card $card)
    {
        if (!$card->is_active) {
            abort(404);
        }

        return response()->json([
            'card' => $card,
            'final_price' => $card->getFinalPrice(),
            'in_stock' => $card->isInStock(),
        ]);
    }
}
